PODS:
  - aliyun_log_dart_sdk (0.0.1):
    - AliyunLogProducer/Producer (= 3.1.13)
    - Flutter
  - AliyunLogProducer/Producer (3.1.13)
  - Flutter (1.0.0)
  - niimbot_log_plugin (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - aliyun_log_dart_sdk (from `.symlinks/plugins/aliyun_log_dart_sdk/ios`)
  - Flutter (from `Flutter`)
  - niimbot_log_plugin (from `.symlinks/plugins/niimbot_log_plugin/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/ios`)

SPEC REPOS:
  trunk:
    - AliyunLogProducer

EXTERNAL SOURCES:
  aliyun_log_dart_sdk:
    :path: ".symlinks/plugins/aliyun_log_dart_sdk/ios"
  Flutter:
    :path: Flutter
  niimbot_log_plugin:
    :path: ".symlinks/plugins/niimbot_log_plugin/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/ios"

SPEC CHECKSUMS:
  aliyun_log_dart_sdk: e0a1d68b1414a77109e75b86738aa6498ab7998e
  AliyunLogProducer: d34977f41687dd4fc510df33157f3d4e4ccf5fd8
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  niimbot_log_plugin: eb0f7721cc3856dbc55a428f4f67aa15f93309c5
  path_provider_foundation: 29f094ae23ebbca9d3d0cec13889cd9060c0e943

PODFILE CHECKSUM: ef19549a9bc3046e7bb7d2fab4d021637c0c58a3

COCOAPODS: 1.12.1
