/// uid : "value"
/// eventType : "business"
/// eventLevel : 1
/// eventName : "print"
/// eventExplain : "value"
/// createTime : "2024-05-13 10:02:06"
/// city : "武汉"
/// phone : "HUAWEI Mate30"
/// deviceId : "111123"
/// eventDetail : {"url":"baidu.com","city":"江苏苏州","country":"中国"}
/// systemVersion : "4.0.0"
/// appVserson : "6.0.8"
/// language : "zh"
/// moudle : "myTemplate"

class NiimbotLogModel {
  NiimbotLogModel({
    String? uid,
    String? eventType,
    num? eventLevel,
    String? eventName,
    String? eventExplain,
    String? createTime,
    String? city,
    String? phone,
    String? deviceId,
    Map<String, dynamic>? eventDetail,
    String? systemVersion,
    String? appVserson,
    String? language,
    String? moudle,
  }) {
    _uid = uid;
    _eventType = eventType;
    _eventLevel = eventLevel;
    _eventName = eventName;
    _eventExplain = eventExplain;
    _createTime = createTime;
    _city = city;
    _phone = phone;
    _deviceId = deviceId;
    _eventDetail = eventDetail;
    _systemVersion = systemVersion;
    _appVserson = appVserson;
    _language = language;
    _moudle = moudle;
  }

  NiimbotLogModel.fromJson(dynamic json) {
    _uid = json['uid'];
    _eventType = json['eventType'];
    _eventLevel = json['eventLevel'];
    _eventName = json['eventName'];
    _eventExplain = json['eventExplain'];
    _createTime = json['createTime'];
    _city = json['city'];
    _phone = json['phone'];
    _deviceId = json['deviceId'];
    _eventDetail = json['eventDetail'] != null ? Map<String, dynamic>.from(json['eventDetail']) : null;
    _systemVersion = json['systemVersion'];
    _appVserson = json['appVserson'];
    _language = json['language'];
    _moudle = json['moudle'];
  }
  String? _uid;
  String? _eventType;
  num? _eventLevel;
  String? _eventName;
  String? _eventExplain;
  String? _createTime;
  String? _city;
  String? _phone;
  String? _deviceId;
  Map<String, dynamic>? _eventDetail;
  String? _systemVersion;
  String? _appVserson;
  String? _language;
  String? _moudle;
  NiimbotLogModel copyWith({
    String? uid,
    String? eventType,
    num? eventLevel,
    String? eventName,
    String? eventExplain,
    String? createTime,
    String? city,
    String? phone,
    String? deviceId,
    Map<String, dynamic>? eventDetail,
    String? systemVersion,
    String? appVserson,
    String? language,
    String? moudle,
  }) =>
      NiimbotLogModel(
        uid: uid ?? _uid,
        eventType: eventType ?? _eventType,
        eventLevel: eventLevel ?? _eventLevel,
        eventName: eventName ?? _eventName,
        eventExplain: eventExplain ?? _eventExplain,
        createTime: createTime ?? _createTime,
        city: city ?? _city,
        phone: phone ?? _phone,
        deviceId: deviceId ?? _deviceId,
        eventDetail: eventDetail ?? _eventDetail,
        systemVersion: systemVersion ?? _systemVersion,
        appVserson: appVserson ?? _appVserson,
        language: language ?? _language,
        moudle: moudle ?? _moudle,
      );
  String? get uid => _uid;
  String? get eventType => _eventType;
  num? get eventLevel => _eventLevel;
  String? get eventName => _eventName;
  String? get eventExplain => _eventExplain;
  String? get createTime => _createTime;
  String? get city => _city;
  String? get phone => _phone;
  String? get deviceId => _deviceId;
  Map<String, dynamic>? get eventDetail => _eventDetail;
  String? get systemVersion => _systemVersion;
  String? get appVserson => _appVserson;
  String? get language => _language;
  String? get moudle => _moudle;

  Future<Map<String, dynamic>> toJson() async {
    final map = <String, dynamic>{};
    map['uid'] = _uid;
    map['eventType'] = _eventType;
    map['eventLevel'] = _eventLevel;
    map['eventName'] = _eventName;
    map['eventExplain'] = _eventExplain;
    map['createTime'] = DateTime.now().microsecondsSinceEpoch;
    map['city'] = _city;
    map['phone'] = _phone;
    map['deviceId'] = _deviceId;
    if (_eventDetail != null) {
      map['eventDetail'] = _eventDetail;
    }
    map['systemVersion'] = _systemVersion;
    map['appVserson'] = _appVserson;
    map['language'] = _language;
    map['moudle'] = _moudle;
    return map;
  }
}
