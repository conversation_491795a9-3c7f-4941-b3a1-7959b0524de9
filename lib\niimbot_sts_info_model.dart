class NiimbotStsInfoModel {
  ///sts凭证信息
  ///用于访问日志/OSS服务的AccessKey ID
  String? accessKeyId;

  ///用于访问日志/OSS服务的AccessKey Secret
  String? accessKeySecret;

  ///访问日志/OSS服务的访问密钥Token。使用令牌服务（STS）方式接入时，需要配置
  String? securityToken;

  ///Project所在地域的服务入口。例如cn-hangzhou.log.aliyuncs.com
  String? endpoint;

  ///Project名称
  String? project;

  ///Logstore名称
  String? logstore;

  ///Logstore名称
  String? cdnEndPorint;

  String? signBody;

  String? signHead;

  ///有效期
  int? expired;

  ///token有效期
  int? tokenExpireTime;

  ///桶名
  String? bucket;

  ///上传文件 路径必须拼接使用 否则阿里云协议报错
  String? dir;

  NiimbotStsInfoModel.fromJson(Map<String, dynamic> jsonMap) {
    accessKeyId = jsonMap["accessKeyId"] ?? "";
    accessKeySecret = jsonMap["accessKeySecret"] ?? "";
    securityToken = jsonMap["securityToken"] ?? "";
    endpoint = jsonMap["endpoint"] ?? "";
    logstore = jsonMap["logStore"] ?? "";
    expired = jsonMap["expired"] ?? 0;
    project = jsonMap["project"] ?? "";
    bucket = jsonMap["bucket"] ?? "";
    dir = jsonMap["dir"] ?? "";
    tokenExpireTime = jsonMap["tokenExpireTime"] ?? 0;
    cdnEndPorint = jsonMap["cdnEndpoint"] ?? "";
    signBody = jsonMap["signBody"] ?? "";
    signHead = jsonMap["signHead"] ?? "";
  }
}
