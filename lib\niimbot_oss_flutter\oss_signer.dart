part of aliyun_oss_flutter;

/// 签名信息类，包含请求头需要的签名数据
class SignedInfo {
  const SignedInfo({
    required this.dateString,
    required this.accessKeyId,
    required this.signature,
    this.securityToken,
  });

  final String dateString; // 请求时间字符串
  final String accessKeyId; // 访问密钥 ID
  final String signature; // 计算出的签名
  final String? securityToken; // 安全令牌（如果有）

  /// 转换为 HTTP 请求头格式
  Map<String, String> toHeaders() => {
        'Date': dateString,
        'Authorization': 'OSS $accessKeyId:$signature',
        if (securityToken != null) 'x-oss-security-token': securityToken!,
      };
}

/// 负责签名的类，使用访问凭据计算请求签名
class Signer {
  const Signer(this.credentials);
  final Credentials credentials; // 访问凭据

  /// 计算签名
  ///
  /// [httpMethod] HTTP 方法（GET、PUT等）
  /// [resourcePath] 资源路径
  /// [parameters] 查询参数
  /// [headers] 请求头
  /// [contentMd5] 可选的 MD5 校验值
  /// [dateString] 请求时间字符串
  SignedInfo sign({
    required String httpMethod,
    required String resourcePath,
    Map<String, String>? parameters,
    Map<String, String>? headers,
    String? contentMd5,
    String? dateString,
  }) {
    // 处理安全头部信息
    final securityHeaders = {
      if (headers != null) ...headers,
      if (credentials.securityToken != null) ...{
        'x-oss-security-token': credentials.securityToken!,
      }
    };

    // 按照键名排序请求头
    final sortedHeaders = _sortByLowerKey(securityHeaders);

    // 提取 Content-Type
    final contentType = sortedHeaders
        .firstWhere(
          (e) => e.key == 'content-type',
          orElse: () => const MapEntry('', ''),
        )
        .value;

    // 生成规范化的 OSS 头部字符串
    final canonicalizedOSSHeaders =
        sortedHeaders.where((e) => e.key.startsWith('x-oss-')).map((e) => '${e.key}:${e.value}').join('\n');

    // 处理查询参数
    final securityParameters = {
      if (parameters != null) ...parameters,
    };
    final canonicalizedResource = _buildCanonicalizedResource(resourcePath, securityParameters);

    // 生成请求时间
    final date = dateString ?? _requestTime();

    // 组装待签名字符串
    final canonicalString = [
      httpMethod,
      contentMd5 ?? '',
      contentType,
      date,
      if (canonicalizedOSSHeaders.isNotEmpty) canonicalizedOSSHeaders,
      canonicalizedResource,
    ].join('\n');

    // 计算 HMAC-SHA1 签名
    final signature = _computeHmacSha1(canonicalString);

    return SignedInfo(
        dateString: date,
        accessKeyId: credentials.accessKeyId,
        signature: signature,
        securityToken: credentials.securityToken);
  }

  /// 构建规范化资源路径
  String _buildCanonicalizedResource(String resourcePath, Map<String, String> parameters) {
    if (parameters.isNotEmpty == true) {
      final queryString = _sortByLowerKey(parameters).map((e) => '${e.key}=${e.value}').join('&');
      return '$resourcePath?$queryString';
    }
    return resourcePath;
  }

  /// 计算 HMAC-SHA1 签名
  String _computeHmacSha1(String plaintext) {
    final digest = Hmac(sha1, utf8.encode(credentials.accessKeySecret)).convert(utf8.encode(plaintext));
    return base64.encode(digest.bytes);
  }

  /// 按键名排序 Map 并去除空格
  List<MapEntry<String, String>> _sortByLowerKey(Map<String, String> map) {
    final lowerPairs = map.entries.map((e) => MapEntry(e.key.toLowerCase().trim(), e.value.toString().trim()));
    return lowerPairs.toList()..sort((a, b) => a.key.compareTo(b.key));
  }

  /// 获取当前时间的 GMT 格式字符串
  String _requestTime() {
    initializeDateFormatting('en', null);
    final DateTime now = DateTime.now();
    final String string = DateFormat('EEE, dd MMM yyyy HH:mm:ss', 'en_ISO').format(now.toUtc());
    return '$string GMT';
  }
}
