import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'niimbot_log_plugin_method_channel.dart';

abstract class NiimbotLogPluginPlatform extends PlatformInterface {
  /// Constructs a NiimbotLogPluginPlatform.
  NiimbotLogPluginPlatform() : super(token: _token);

  static final Object _token = Object();

  static NiimbotLogPluginPlatform _instance = MethodChannelNiimbotLogPlugin();

  /// The default instance of [NiimbotLogPluginPlatform] to use.
  ///
  /// Defaults to [MethodChannelNiimbotLogPlugin].
  static NiimbotLogPluginPlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [NiimbotLogPluginPlatform] when
  /// they register themselves.
  static set instance(NiimbotLogPluginPlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  Future<String?> getPlatformVersion() {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }

  setupNativeCommunication() {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }
}
