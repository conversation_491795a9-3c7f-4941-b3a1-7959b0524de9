# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  args:
    dependency: transitive
    description:
      name: args
      sha256: "7cf60b9f0cc88203c5a190b4cd62a99feea42759a7fa695010eb5de1c0b2252a"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.5.0"
  asn1lib:
    dependency: transitive
    description:
      name: asn1lib
      sha256: "58082b3f0dca697204dbab0ef9ff208bfaea7767ea771076af9a343488428dda"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.5.3"
  async:
    dependency: transitive
    description:
      name: async
      sha256: "947bfcf187f74dbc5e146c9eb9c0f10c9f8b30743e341481c1e2ed3ecc18c20c"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.11.0"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "6cfb5af12253eaf2b368f07bacc5a80d1301a071c73360d746b7f2e32d762c66"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.1"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: "04a925763edad70e8443c99234dc3328f442e811f1d8fd1a72f1c8ad0f69a605"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.3.0"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: cb6d7f03e1de671e34607e909a7213e31d7752be4fb66a86d29fe1eb14bfb5cf
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.1"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: ee67cb0715911d28db6bf4af1026078bd6f0128b07a5f66fb2ed94ec6783c09a
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.18.0"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: "0f08b14755d163f6e2134cb58222dd25ea2a2ee8a195e53983d57c075324d592"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.1.1"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: ff625774173754681d66daaf4a448684fb04b78f902da9cb3d308c19cc5e8bab
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.3"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      sha256: ba631d1c7f7bef6b729a622b7b752645a2d076dba9976925b8f25725a30e1ee6
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.8"
  dio:
    dependency: transitive
    description:
      name: dio
      sha256: "0dfb6b6a1979dac1c1245e17cef824d7b452ea29bd33d3467269f9bef3715fb0"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "5.6.0"
  dio_web_adapter:
    dependency: transitive
    description:
      name: dio_web_adapter
      sha256: "33259a9276d6cea88774a0000cfae0d861003497755969c92faa223108620dc8"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.0"
  encrypt:
    dependency: transitive
    description:
      name: encrypt
      sha256: "62d9aa4670cc2a8798bab89b39fc71b6dfbacf615de6cf5001fb39f7e4a996a2"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "5.0.3"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "511392330127add0b769b75a987850d136345d9227c6b94c96a04cf4a391bf78"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.3.1"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: "493f37e7df1804778ff3a53bd691d8692ddf69702cf4c1c1096a2e41b4779e21"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.2"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: "25517a4deb0c03aa0f32fd12db525856438902d9c16536311e76cdc57b31d7d1"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.0"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_easyloading:
    dependency: transitive
    description:
      name: flutter_easyloading
      sha256: ba21a3c883544e582f9cc455a4a0907556714e1e9cf0eababfcb600da191d17c
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.5"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      sha256: a25a15ebbdfc33ab1cd26c63a6ee519df92338a9c10f122adda92938253bef04
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.3"
  flutter_spinkit:
    dependency: transitive
    description:
      name: flutter_spinkit
      sha256: d2696eed13732831414595b98863260e33e8882fc069ee80ec35d4ac9ddb0472
      url: "https://pub.niimbot.info"
    source: hosted
    version: "5.2.1"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.0.2"
  intl:
    dependency: transitive
    description:
      name: intl
      sha256: d6f56758b7d3014a48af9701c085700aac781a92a87a62b1333b46d8879661cf
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.19.0"
  js:
    dependency: transitive
    description:
      name: js
      sha256: c1b2e9b5ea78c45e1a0788d29606ba27dc5f71f019f32ca5140f61ef071838cf
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.7.1"
  leak_tracker:
    dependency: transitive
    description:
      name: leak_tracker
      sha256: "7f0df31977cb2c0b88585095d168e689669a2cc9b97c309665e3386f3e9d341a"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "10.0.4"
  leak_tracker_flutter_testing:
    dependency: transitive
    description:
      name: leak_tracker_flutter_testing
      sha256: "06e98f569d004c1315b991ded39924b21af84cf14cc94791b8aea337d25b57f8"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.3"
  leak_tracker_testing:
    dependency: transitive
    description:
      name: leak_tracker_testing
      sha256: "6ba465d5d76e67ddf503e1161d1f4a6bc42306f9d66ca1e8f079a47290fb06d3"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.1"
  lints:
    dependency: transitive
    description:
      name: lints
      sha256: "0a217c6c989d21039f1498c3ed9f3ed71b354e69873f13a8dfc3c9fe76f1b452"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.1"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: d2323aa2060500f906aa31a895b4030b6da3ebdcc5619d14ce1aada65cd161cb
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.12.16+1"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: "0e0a020085b65b6083975e499759762399b4475f766c21668c4ecca34ea74e5a"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.8.0"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: "7687075e408b093f36e6bbf6c91878cc0d4cd10f409506f7bc996f68220b9136"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.12.0"
  niimbot_log_plugin:
    dependency: "direct main"
    description:
      path: ".."
      relative: true
    source: path
    version: "0.0.1"
  niimbot_sls_flutter:
    dependency: transitive
    description:
      path: "."
      ref: master
      resolved-ref: dcc73f80a3ccec1102bc150629660f39812f1c8e
      url: "https://git.jc-ai.cn/print/foundation/niimbot_sls_flutter.git"
    source: git
    version: "1.1.2"
  path:
    dependency: transitive
    description:
      name: path
      sha256: "087ce49c3f0dc39180befefc60fdb4acd8f8620e5682fe2476afd0b3688bb4af"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.9.0"
  path_provider:
    dependency: transitive
    description:
      name: path_provider
      sha256: c9e7d3a4cd1410877472158bee69963a4579f78b68c65a2b7d40d1a7a88bb161
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.3"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: bca87b0165ffd7cdb9cad8edd22d18d2201e886d9a9f19b4fb3452ea7df3a72a
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.2.6"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: f234384a3fdd67f989b4d54a5d73ca2a6c422fa55ae694381ae0f4375cd1ea16
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.0"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.2.1"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "88f5779f72ba699763fa3a3b06aa4bf6de76c8e5de842cf6f29e2e06476c2334"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.2"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: "8bc9f22eee8690981c22aa7fc602f5c85b497a6fb2ceb35ee5a5e5ed85ad8170"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.2.1"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "9b71283fc13df574056616011fb138fd3b793ea47cc509c189a6c3fa5f8a1a65"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.1.5"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "4820fbfdb9478b1ebae27888254d445073732dae3d6ea81f0b7e06d5dedc3f02"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.8"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      sha256: "4be0097fcf3fd3e8449e53730c631200ebc7b88016acecab2b0da2f0149222fe"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.9.1"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "53e943d4206a5e30df338fd4c6e7a077e02254531b138a15aec3bd143c1a8b3c"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.10.0"
  sprintf:
    dependency: transitive
    description:
      name: sprintf
      sha256: "1fc9ffe69d4df602376b52949af107d8f5703b77cda567c4d7d86a0693120f23"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "7.0.0"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "73713990125a6d93122541237550ee3352a2d84baad52d375a4cad2eb9b7ce0b"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.11.1"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: ba2aa5d8cc609d96bbb2899c28934f9e1af5cddbd60a827822ea467161eb54e7
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.2"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "556692adab6cfa87322a115640c11f13cb77b3f076ddcc5d6ae3c20242bedcde"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.0"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.1"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: "9955ae474176f7ac8ee4e989dadfb411a58c30415bcfb648fa04b2b8a03afa7f"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.7.0"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: facc8d6582f16042dd49f2463ff1bd6e2c9ef9f3d5da3d9b087e244a7b564b3c
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.3.2"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: "83d37c7ad7aaf9aa8e275490669535c8080377cfa7a7004c24dfac53afffaa90"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.4.2"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.4"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: "3923c89304b715fb1eb6423f017651664a03bf5f4b29983627c4da791f74a4ec"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "14.2.1"
  web:
    dependency: transitive
    description:
      name: web
      sha256: d43c1d6b787bf0afad444700ae7f4db8827f701bc61c255ac8d328c6f4d52062
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.0"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: a79dbe579cb51ecd6d30b17e0cae4e0ea15e2c0e66f69ad4198f22a6789e94f4
      url: "https://pub.niimbot.info"
    source: hosted
    version: "5.5.1"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: faea9dee56b520b55a566385b84f2e8de55e7496104adada9962e0bd11bcff1d
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.4"
sdks:
  dart: ">=3.4.0 <4.0.0"
  flutter: ">=3.22.0"
