class NiimbotAppConfig {
  ///app api请求域名
  late final String apiBaseUrl;

  ///应用 userAgent
  String userAgent = "";

  ///设备型号 iPhone 16/ HuaWei P7
  late final String phone;

  ///设备id  9fj93-f3j9f3-3j9f3j0-234mf93-d39j2
  String deviceId = "";

  ///系统版本  17.4.1
  late final String systemVersion;

  ///app版本 6.0.9
  late final String appVserson;

  ///系统类型    ios/Android
  late final String os;

  ///用户token
  late String token;

  ///用户uid
  late String uid;

  ///应用语言类型 en/zh-cn
  late String language;

  ///当前连接打印机
  late String currentConnectPrint;

  NiimbotAppConfig(
      {required this.apiBaseUrl,
      required this.userAgent,
      this.language = "zh-cn",
      this.token = "",
      this.uid = "",
      this.phone = "",
      this.deviceId = "",
      this.systemVersion = "",
      this.appVserson = "",
      this.currentConnectPrint = "",
      this.os = ""});

  ///转换json 用于上传日志附带配置信息
  Map<String, dynamic> toJson() {
    Map<String, dynamic> appInfo = {};
    appInfo["phone"] = phone;
    appInfo["uid"] = uid;
    appInfo["deviceId"] = deviceId;
    appInfo["systemVersion"] = systemVersion;
    appInfo["appVserson"] = appVserson;
    appInfo["language"] = language;
    appInfo["os"] = os;
    appInfo["currentConnectedPrint"] = currentConnectPrint;
    return appInfo;
  }

  NiimbotAppConfig.fromJson(Map<String, dynamic> json) {
    apiBaseUrl = json['apiBaseUrl'] ?? "";
    userAgent = json['userAgent'] ?? "";
    language = json['language'] ?? "";
    token = json['token'] ?? "";
    uid = json['uid'] ?? "";
    phone = json['phone'] ?? "";
    deviceId = json['deviceId'] ?? "";
    systemVersion = json['systemVersion'] ?? "";
    appVserson = json['appVserson'] ?? "";
    os = json['os'] ?? "";
    currentConnectPrint = json['currentConnectPrint'] ?? "";
  }
}
