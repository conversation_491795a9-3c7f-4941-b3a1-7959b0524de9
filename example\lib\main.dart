import 'package:flutter/material.dart';
import 'dart:async';
import 'package:flutter/services.dart';
import 'package:niimbot_log_plugin/niimbot_log_plugin.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

void main() {
  runApp(const MyApp());
  configLoading();
}

void configLoading() {
  EasyLoading.instance
    ..displayDuration = const Duration(milliseconds: 2000)
    ..indicatorType = EasyLoadingIndicatorType.fadingCircle
    ..loadingStyle = EasyLoadingStyle.dark
    ..indicatorSize = 45.0
    ..radius = 10.0
    ..progressColor = Colors.yellow
    ..backgroundColor = Colors.green
    ..indicatorColor = Colors.yellow
    ..textColor = Colors.yellow
    ..maskColor = Colors.blue.withOpacity(0.5)
    ..userInteractions = true
    ..dismissOnTap = false;
  // ..customAnimation = CustomAnimation();
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  String _platformVersion = 'Unknown';
  final _niimbotLogPlugin = NiimbotLogTool();

  @override
  void initState() {
    super.initState();
    initPlatformState();
    NiimbotLogTool.initLogConfig(NiimbotAppConfig(
        apiBaseUrl: "https://print.niimbot.com/api/",
        phone: "iphone 15",
        systemVersion: "17.4.1",
        appVserson: "6.5.0",
        os: "ios",
        deviceId: "deoekogekogkeokgeo",
        uid: "6R87Y9TE",
        userAgent:
            "AppId/com.suofang.jcbqdy OS/ios AppVersionName/6.0.8 Model/iPhone-11 SystemVersion/14.6 DeviceId/EA400739-C714-45B2-9A73-100B8AAEBFC1 boundleId/com.suofang.jcbqdy referer/CP001Mobile",
        language: "en"));
  }

  // Platform messages are asynchronous, so we initialize in an async method.
  Future<void> initPlatformState() async {
    String platformVersion;
    // Platform messages may fail, so we use a try/catch PlatformException.
    // We also handle the message potentially returning null.
    try {
      platformVersion = 'Failed to get platform version.';
    } on PlatformException {
      platformVersion = 'Failed to get platform version.';
    }

    // If the widget was removed from the tree while the asynchronous platform
    // message was in flight, we want to discard the reply rather than calling
    // setState to update our non-existent appearance.
    if (!mounted) return;

    setState(() {
      _platformVersion = platformVersion;
    });
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Plugin example app'),
        ),
        body: Center(
            child: Column(
          children: [
            const SizedBox(
              height: 50,
            ),
            GestureDetector(
              onTap: () {
                NiimbotLogTool.uploadLogInstantTime({"device": "12312"});
                NiimbotLogTool.uploadLogInstantTime({"device": "12312111"},
                    logSTSType: NiimbotLogSTSType.printContentSTS);
              },
              child: const Text('即时上传'),
            ),
            const SizedBox(
              height: 50,
            ),
            GestureDetector(
              onTap: () {
                for (int index = 0; index < 100; index++) {
                  NiimbotLogTool.writeLogToFile({"key$index": "${index * 2}"});
                }
              },
              child: const Text('写入日志'),
            ),
            const SizedBox(
              height: 50,
            ),
            GestureDetector(
              onTap: () {
                EasyLoading.show();
                NiimbotLogTool.uploadLogManual(
                  success: (String url) {
                    EasyLoading.dismiss();
                    EasyLoading.showSuccess("成功");
                  },
                  faild: (NiimbotLogResultType errorCode, String msg) {
                    EasyLoading.dismiss();
                    EasyLoading.showSuccess(msg);
                  },
                );
              },
              child: const Text('上传日志'),
            ),
          ],
        )),
      ),
      builder: EasyLoading.init(),
    );
  }
}
