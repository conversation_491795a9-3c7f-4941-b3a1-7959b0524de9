{
    // 使用 IntelliSense 了解相关属性。 
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": " niimbot_log_plugin",
            "request": "launch",
            "type": "dart",
            "flutterMode": "debug"
        },
        {
            "name": "FlutterAttach",
            "type": "dart",
            "request": "attach",
            "args": [
                "--app-id",
                "com.suofang.jcbqdy.logPlugin"
            ]
        },
        {
            "name": " niimbot_log_plugin (profile mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": " niimbot_log_plugin (release mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "example",
            "cwd": "example",
            "type": "dart",
            "request": "attach",
            "args": [
                "--app-id",
                "com.suofang.jcbqdy.logPlugin"
            ]
        },
        {
            "name": "example (profile mode)",
            "cwd": "example",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "example (release mode)",
            "cwd": "example",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        }
    ]
}