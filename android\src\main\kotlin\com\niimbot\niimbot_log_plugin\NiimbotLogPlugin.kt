package com.niimbot.niimbot_log_plugin

import androidx.annotation.NonNull

import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result

/** NiimbotLogPlugin */
class NiimbotLogPlugin: FlutterPlugin, MethodCallHandler {
  /// The MethodChannel that will the communication between Flutter and native Android
  ///
  /// This local reference serves to register the plugin with the Flutter Engine and unregister it
  /// when the Flutter Engine is detached from the Activity


  // 静态变量来保存单例实例
  companion object {
    private var instance: NiimbotLogPlugin? = null

    // 获取单例实例的静态方法
    fun getInstance(): NiimbotLogPlugin {
      if (instance == null) {
        instance = NiimbotLogPlugin()
      }
      return instance!!
    }
  }


  override fun onAttachedToEngine(@NonNull flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
    NiimbotLogBridge.getInstance().initChannel(flutterPluginBinding).setMethodCallHandler(this)
  }

  override fun onMethodCall(@NonNull call: MethodCall, @NonNull result: Result) {
    if (call.method == "getPlatformVersion") {
      result.success("Android ${android.os.Build.VERSION.RELEASE}")
    } else {
      result.notImplemented()
    }
  }

  override fun onDetachedFromEngine(@NonNull binding: FlutterPlugin.FlutterPluginBinding) {
    NiimbotLogBridge.getInstance().onDetachedFromEngine()
  }
}
