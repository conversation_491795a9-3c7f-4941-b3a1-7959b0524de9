import 'package:flutter_test/flutter_test.dart';
import 'package:niimbot_log_plugin/niimbot_log_plugin.dart';
import 'package:niimbot_log_plugin/niimbot_log_plugin_platform_interface.dart';
import 'package:niimbot_log_plugin/niimbot_log_plugin_method_channel.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

class MockNiimbotLogPluginPlatform
    with MockPlatformInterfaceMixin
    implements NiimbotLogPluginPlatform {

  @override
  Future<String?> getPlatformVersion() => Future.value('42');
}

void main() {
  final NiimbotLogPluginPlatform initialPlatform = NiimbotLogPluginPlatform.instance;

  test('$MethodChannelNiimbotLogPlugin is the default instance', () {
    expect(initialPlatform, isInstanceOf<MethodChannelNiimbotLogPlugin>());
  });

  test('getPlatformVersion', () async {
    NiimbotLogPlugin niimbotLogPlugin = NiimbotLogPlugin();
    MockNiimbotLogPluginPlatform fakePlatform = MockNiimbotLogPluginPlatform();
    NiimbotLogPluginPlatform.instance = fakePlatform;

    expect(await niimbotLogPlugin.getPlatformVersion(), '42');
  });
}
