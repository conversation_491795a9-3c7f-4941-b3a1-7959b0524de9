import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:niimbot_sls_flutter/aliyun_log_dart_sdk.dart';
import 'package:path_provider/path_provider.dart';

import './niimbot_oss_flutter/niimbot_oss_flutter.dart';
import './niimbot_sts_info_model.dart';
import 'model/niimbot_app_config.dart';
import 'model/niimbot_log_result.dart';

class NiimbotLogManager {
  static NiimbotLogManager? _instance;
  // Avoid self instance
  NiimbotLogManager._();
  static NiimbotLogManager get instance => _instance ??= NiimbotLogManager._();

  ///不同场景SLS LogSDK信息
  Map<String, AliyunLogDartSdk> sdkObjInfos = {};

  ///sts dio
  Dio dio = Dio();

  ///文件持续写入字符流 使用避免多次打开文件导致异常
  late IOSink _sink;

  ///本地日志文件
  late File _file;

  ///默认日志文件地址

  late String _defaultLogFilePath;

  ///OSS STS凭证信息
  NiimbotStsInfoModel? ossStsModel;

  ///SLS STS凭证信息
  NiimbotStsInfoModel? slsModel;

  ///应用配置（baseurl，token，userAgent....）
  late NiimbotAppConfig _appConfig;

  ///初始化网络环境
  void initConfig(NiimbotAppConfig appConfig) async {
    _appConfig = appConfig;
    dio.options = BaseOptions(baseUrl: _appConfig.apiBaseUrl, headers: {
      "languageCode": _appConfig.language,
      "niimbot-user-agent": _appConfig.userAgent,
      "Authorization": _appConfig.token
    });
    dio.options.extra = {"needEncrypt": true};
    // dio.interceptors.add(NiimbotInterceptor());
    await initFileOperate();
  }

  ///更新deviceId
  void updateDeviceId(String deviceId, String agent) {
    _appConfig.deviceId = deviceId;
    _appConfig.userAgent = agent;
    dio.options = BaseOptions(baseUrl: _appConfig.apiBaseUrl, headers: {
      "languageCode": _appConfig.language,
      "niimbot-user-agent": _appConfig.userAgent,
      "Authorization": _appConfig.token
    });
  }

  ///初始化网络环境
  void updateConfig(String token, String uid, String languageCode, String currentConnectPrint) async {
    _appConfig.token = token;
    _appConfig.uid = uid;
    _appConfig.language = languageCode;
    _appConfig.currentConnectPrint = currentConnectPrint;
    dio.options = BaseOptions(baseUrl: _appConfig.apiBaseUrl, headers: {
      "languageCode": _appConfig.language,
      "niimbot-user-agent": _appConfig.userAgent,
      "Authorization": _appConfig.token,
    });
  }

  ///日志配置初始化
  LogProducerConfiguration _logConfiguration(NiimbotStsInfoModel slsStsModel) {
    LogProducerConfiguration configuration = LogProducerConfiguration(
        endpoint: slsStsModel.endpoint, project: slsStsModel.project, logstore: slsStsModel.logstore);
    configuration.accessKeyId = slsStsModel.accessKeyId;
    configuration.accessKeySecret = slsStsModel.accessKeySecret;
    configuration.securityToken = slsStsModel.securityToken; // 使用令牌服务（STS）方式获取临时AccessKey配置。
    return configuration;
  }

  ///添加实时日志
  Future<bool> addAppLogInstandTime(Map<String, dynamic> logInfo, {String path = "", String topic = ""}) async {
    debugPrint('--------sls添加日志:$path 发送数据:$logInfo');
    AliyunLogDartSdk? currentAliyunLogSdk = sdkObjInfos[path];
    //当前场景日志对象不存在则初始化一个
    if (currentAliyunLogSdk == null) {
      currentAliyunLogSdk = await _initAliyunLogDartSdk(path);
      if (currentAliyunLogSdk != null) {
        sdkObjInfos[path] = currentAliyunLogSdk;
      }
      if (currentAliyunLogSdk == null) return Future.value(false);
    }
    logInfo["time"] = DateTime.now().toString();
    logInfo.addAll(_appConfig.toJson());
    await currentAliyunLogSdk.setTopic(topic);
    LogProducerResult code = await currentAliyunLogSdk.addLog(logInfo);
    debugPrint('--------sls添加日志发送结果:$code 发送数据:$logInfo');
    return Future.value(code == LogProducerResult.ok);
  }

  Future<void> getPrintTemplateContentSlsModel(String path) async {
    await _getSTSInfoFromServer(path);
  }

  static Map<String, File> fileOperate = {};

  ///写入日志
  Future<bool> writeAppLogTofile(Map<String, dynamic> logInfo, {String path = ""}) async {
    //logInfo转Json
    String logInfoStr = jsonEncode(logInfo);
    //logInfoStr存入文件时以格式化的方式进行显示
    var prettyJson = const JsonEncoder.withIndent('\n').convert(logInfoStr);
    writeData("${DateTime.now().toString()}: \n$prettyJson");
    return Future.value(true);
  }

  ///请求相应路径下STS信息
  Future<LogProducerConfiguration?> _getSTSInfoFromServer(String path) async {
    try {
      Response response = await dio.request(
        path,
        options: Options(method: 'GET'),
      );
      if (response.data != null) {
        Map data = response.data!["data"];
        NiimbotStsInfoModel slsStsModel = NiimbotStsInfoModel.fromJson(Map<String, dynamic>.from(data));
        if (path == "sts/template/content") {
          slsModel = slsStsModel;
        }
        LogProducerConfiguration configuration = _logConfiguration(slsStsModel);
        return Future.value(configuration);
      } else {
        return null;
      }
    } on DioException catch (e) {
      NiimbotLogResultType exceptionType = getNiimbotLogResultType(e);
      debugPrint("请求STS 信息异常 $exceptionType");
      return null;
    }
  }

  static bool isRequestToken = false;

  ///初始化一个日志服务对象
  Future<AliyunLogDartSdk?> _initAliyunLogDartSdk(String path) async {
    //获取STS信息
    if (isRequestToken) {
      return Future.value();
    }
    isRequestToken = true;
    LogProducerConfiguration? configuration = await _getSTSInfoFromServer(path);
    isRequestToken = false;
    if (configuration == null) {
      return null;
    }
    //创建日志服务对象
    AliyunLogDartSdk currentAliyunLogSdk = AliyunLogDartSdk();
    //根据STS配置信息初始化日志对象
    LogProducerResult code = await currentAliyunLogSdk.initProducer(configuration);
    debugPrint("初始化结果:$code");
    currentAliyunLogSdk.setLogCallback((resultCode, errorMessage, logBytes, compressedBytes) async {
      // 参数配置错误，需要更新参数。
      debugPrint("--------sls--------回调结果$resultCode");
      if (LogProducerResult.sendUnauthorized == resultCode || LogProducerResult.parametersInvalid == resultCode) {
        if (!isRequestToken) {
          isRequestToken = true;
          try {
            await resetSTSAliyunLogSdkInof();
            isRequestToken = false;
          } catch (e) {
            isRequestToken = false;
          }
        }
      }
    });
    return Future.value(currentAliyunLogSdk);
  }

  resetSTSAliyunLogSdkInof() async {
    for (var element in sdkObjInfos.keys) {
      AliyunLogDartSdk currentAliyunLogSdk = sdkObjInfos[element]!;
      LogProducerConfiguration? configuration = await _getSTSInfoFromServer(element);
      if (configuration != null) {
        await currentAliyunLogSdk.updateConfiguration(configuration);
      }
    }
  }

  ///初始化日志文件写入配置
  Future<void> initFileOperate() async {
    DateFormat dateFormat = DateFormat("yyyy-MM-dd");
    String fileName = '${dateFormat.format(DateTime.now())}.txt';
    final directory = await getApplicationDocumentsDirectory();
    String path = '${directory.path}/Log';
    await deleteFilesOlderThan7Days(path);
    String filePath = '$path/$fileName';
    _defaultLogFilePath = filePath;
    Directory logDirectory = Directory(path);
    bool isDirectoryExist = await logDirectory.exists();
    if (!isDirectoryExist) await logDirectory.create();
    _file = File(filePath);
    // Buffer the file for efficient writing
    _sink = _file.openWrite(mode: FileMode.append);
  }

  /// 清理 7 天前的日志文件，防止存储过载
  Future<void> deleteFilesOlderThan7Days(String directoryPath) async {
    // 打开目录
    Directory directory = Directory(directoryPath);
    if (await directory.exists()) {
      // 列出目录中的文件
      List<FileSystemEntity> files = directory.listSync();

      // 当前时间
      DateTime now = DateTime.now();

      // 遍历文件
      for (FileSystemEntity entity in files) {
        // 只处理文件，忽略子目录等
        if (entity is File) {
          // 获取文件信息
          File file = File(entity.path);
          DateTime modified = await file.lastModified();

          // 计算与当前时间的时间差
          Duration difference = now.difference(modified);

          // 如果修改时间在7天以上，则删除文件
          if (difference.inDays > 7) {
            try {
              await file.delete();
              debugPrint('Deleted file: ${file.path}');
            } catch (e) {
              debugPrint('Failed to delete file: ${file.path}');
            }
          }
        }
      }
    } else {
      debugPrint('Directory not found: $directoryPath');
    }
  }

  ///写入日志数据至本地文件
  Future<void> writeData(String data) async {
    if (_sink != null) {
      _sink.writeln(data);
    } else {
      await _file.writeAsString('$data\n', flush: true);
    }
  }

  ///关闭IOSink
  Future<void> close() async {
    await _sink.close();
  }

  ///上传OSS文件
  uploadFunction(
    Function(NiimbotLogResultType, String) uploadSuccess,
    String fileLocalPath, {
    String? fileName,
  }) async {
    //创建本地文件对象
    bool fileExist = await File(fileLocalPath).exists();
    if (!fileExist) {
      uploadSuccess(NiimbotLogResultType.unknown, "文件不存在");
      return;
    }
    OSSVideoObject object1 = OSSVideoObject.fromFile(file: File(fileLocalPath), uuid: fileName);
    try {
      final object = await OSSClient()
          .putObject(
        object: object1,
        bucket: ossStsModel!.bucket,
        endpoint: ossStsModel!.endpoint,
        path: ossStsModel!.dir!,
      )
          .then((value) {
        if (object1.url.isNotEmpty) {
          uploadSuccess.call(NiimbotLogResultType.success, object1.url);
        } else {
          uploadSuccess.call(NiimbotLogResultType.unknown, "");
        }
      });
    } catch (e) {
      Response? response = (e as DioException).response;
      if (response?.statusCode == 403) {
        ossStsModel = null;
        uploadFile(uploadSuccess, filePath: fileLocalPath, fileName: fileName);
      } else {
        NiimbotLogResultType exceptionType = getNiimbotLogResultType(e);
        uploadSuccess.call(exceptionType, response?.data);
      }
    }
  }

  ///判断获取STS凭证上传文件
  uploadFile(Function(NiimbotLogResultType, String) uploadResult,
      {String filePath = "", String? fileName, String module = "USER_TEMPLATE", bool needDeleteFile = false}) async {
    if (filePath.isEmpty) {
      //文件路径为空 默认上传本地全局日志
      filePath = _defaultLogFilePath;
    }
    if (ossStsModel == null) {
      Map data = {};
      //获取Sts凭证信息
      try {
        var response = await dio.request(
          'sts/ossToken',
          data: {"module": module, "param": _appConfig.deviceId},
          options: Options(method: 'POST'),
        );
        data = response.data!["data"];
      } on DioException catch (e) {
        NiimbotLogResultType exceptionType = getNiimbotLogResultType(e);
        uploadResult(exceptionType, e.message ?? '');
        return;
      }
      NiimbotStsInfoModel stsInfoModel = NiimbotStsInfoModel.fromJson(Map<String, dynamic>.from(data));
      ossStsModel = stsInfoModel;
      //初始化OSS客户端
      OSSClient.init(
        endpoint: ossStsModel!.endpoint!,
        bucket: ossStsModel!.bucket!,
        credentials: () {
          Credentials credentials = Credentials(
              accessKeyId: ossStsModel!.accessKeyId!,
              accessKeySecret: ossStsModel!.accessKeySecret!,
              securityToken: ossStsModel!.securityToken,
              expiration: DateTime.fromMillisecondsSinceEpoch(ossStsModel!.expired!.toInt()));
          return Future.value(credentials);
        },
      );
      //上传OSS文件
      uploadFunction((NiimbotLogResultType exceptionType, String url) {
        if (exceptionType == NiimbotLogResultType.success && needDeleteFile) {
          if (filePath == _defaultLogFilePath) {
            //上传文件成功后删除
            _file.delete();
            //重新创建日志文件
            initFileOperate();
          } else {
            File localFile = File(filePath);
            localFile.delete();
          }
        }
        uploadResult(NiimbotLogResultType.success, url);
      }, filePath,fileName: fileName);
    } else {
      //上传OSS文件
      uploadFunction(uploadResult, filePath,fileName: fileName);
    }
  }

  ///处理日志异常
  NiimbotLogResultType getNiimbotLogResultType(DioException e) {
    NiimbotLogResultType exceptionType = NiimbotLogResultType.unknown;
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        // 连接超时处理
        debugPrint('连接超时');
        exceptionType = NiimbotLogResultType.connectionTimeout;
        break;
      case DioExceptionType.sendTimeout:
        // 发送超时处理
        debugPrint('发送超时');
        exceptionType = NiimbotLogResultType.sendTimeout;
        break;
      case DioExceptionType.receiveTimeout:
        // 接收超时处理
        debugPrint('接收超时');
        exceptionType = NiimbotLogResultType.receiveTimeout;
        break;
      case DioExceptionType.badResponse:
        // 服务器响应错误处理
        debugPrint('服务器响应错误，状态码：${e.response?.statusCode}');
        exceptionType = NiimbotLogResultType.badResponse;
        break;
      case DioExceptionType.cancel:
        // 请求取消处理
        debugPrint('请求被取消');
        exceptionType = NiimbotLogResultType.cancel;
        break;
      default:
        // 其他错误处理
        debugPrint('未知错误');
        exceptionType = NiimbotLogResultType.unknown;
        break;
    }
    return exceptionType;
  }
}
