import 'dart:convert';
import 'dart:math';

import 'package:dio/dio.dart';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart' as encryption;
import 'dart:io';

import 'package:flutter/foundation.dart';

class NiimbotInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    bool needEncrypt = options.extra['needEncrypt'] as bool;
    debugPrint("===============${options.path}, 需要加密：$needEncrypt");
    if (needEncrypt) {
      var random = Random.secure();
      int randomInt = 100000 + random.nextInt(899999);
      options.headers["niimbot-user-agent"] = options.headers["niimbot-user-agent"] + " count/$randomInt";
      debugPrint("===============请求头：${options.headers["niimbot-user-agent"]}");

      String agent = options.headers["niimbot-user-agent"] ?? "";
      if (options.data != null && options.headers["niimbot-user-agent"] != null) {
        String paramString = jsonEncode(options.data);
        var resStr = _dealWithEncypt(paramString, agent, 0, authorization: false);
        options.data = {"data": resStr};
        debugPrint("===============加密参数：${options.data}");
      }
    }
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    final responseBody = response.data;
    final agent = response.requestOptions.headers['niimbot-user-agent'];
    final hasEncryption = agent.contains('count/');

    // debugPrint("===============${response.requestOptions.path}, 需要解密：$hasEncryption, token: ${Application.token}");
    if (hasEncryption) {
      // debugPrint("===============${response.requestOptions.path}解密前：$responseBody");
      response.data = _dealWithEncypt(responseBody, agent, 1, authorization: false);
      // debugPrint("===============${response.requestOptions.path}解密后：${response.data}");
    }
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    super.onError(err, handler);
  }

  String? _dealWithEncypt(String? content, String? agent, int type, {bool authorization = false, String token = ""}) {
    debugPrint("${type == 0 ? '<加密>' : '<解密>'}的原始参数:$content");
    if (content != null && agent != null) {
      String paramString = content!;
      //将请求头中agent处理成数组, 方便后期使用
      List<String> agentComponents = agent.split(" ");
      List<String> newAgentComponents = [];
      String? blankValue, lastItemInNewAgentComponents;
      for (String e in agentComponents) {
        if (!e.contains("/")) {
          if (blankValue != null) {
            blankValue = blankValue! + " " + e;
          } else {
            blankValue = e;
          }
        } else {
          if (blankValue != null) {
            lastItemInNewAgentComponents = newAgentComponents.last;
            lastItemInNewAgentComponents = lastItemInNewAgentComponents! + " " + blankValue!;
            newAgentComponents.removeLast();
            newAgentComponents.add(lastItemInNewAgentComponents!);
            blankValue = null;
          }
          newAgentComponents.add(e);
        }
      }
      debugPrint("用来${type == 0 ? '<加密>' : '<解密>'}的请求头:$newAgentComponents");
      String randomString = agentComponents.last;
      randomString = randomString.split("/").last;
      debugPrint("用来${type == 0 ? '<加密>' : '<解密>'}的count:$randomString");
      String keyStr = "", ivStr = "", key = "", iv = "";
      for (int i = 0; i < randomString.length; i++) {
        String index = randomString.substring(i, i + 1);
        String agentValueAtIndex = "";
        if (int.parse(index) < newAgentComponents.length) {
          agentValueAtIndex = newAgentComponents[int.parse(index)];
        }

        if (agentValueAtIndex != null) {
          var agentComponentsAtIndex = agentValueAtIndex.split("/");
          String valueAtIndex = agentComponentsAtIndex.first;
          String keyAtIndex = agentComponentsAtIndex.last;
          if (valueAtIndex.length > int.parse(index)) {
            ivStr = ivStr + valueAtIndex.substring(int.parse(index), 1 + int.parse(index));
          } else if (keyAtIndex.length > int.parse(index)) {
            ivStr = ivStr + keyAtIndex.substring(int.parse(index), 1 + int.parse(index));
          }
          keyStr = keyStr + keyAtIndex;
        }
      }
      iv = ivStr;
      int needAddCount = 0;
      int needAddStart = 0;
      if (iv.length < 16) {
        needAddStart = iv.length;
        needAddCount = 16 - iv.length;
        for (int index = 0; index < needAddCount; index++) {
          iv = iv + "0";
        }
      }
      if (authorization) {
        keyStr = keyStr + (token.contains("bearer") ? token : "bearer " + token);
      }
      debugPrint("未加密的keyStr:$keyStr");
      List<int> bytes = utf8.encode(keyStr);
      var sha256KeyStr;
      int intV;
      if (Platform.isIOS) {
        intV = int.parse(randomString) % 19;
        sha256KeyStr = sha256.convert(bytes).toString();
      } else {
        intV = int.parse(randomString) % 17;
        sha256KeyStr = sha1.convert(bytes).toString();
      }
      debugPrint("sha256Key:$sha256KeyStr");
      if (sha256KeyStr.length >= 32 + intV) {
        key = sha256KeyStr.substring(intV, 32 + intV);
      } else {
        key = sha256KeyStr.substring(intV);
        key = key.padRight(32, "0");
      }

      List<int> ivData = utf8.encode(iv);
      debugPrint("${type == 0 ? '<加密>' : '<解密>'}的key:$key, ivData:$ivData, iv:$iv");
      var finalKey = encryption.Key.fromUtf8(key);
      var finalIv = encryption.IV.fromUtf8(iv);
      var encrypter = encryption.Encrypter(encryption.AES(finalKey, mode: encryption.AESMode.cbc));
      var resStr;
      try {
        if (type == 0) {
          var encrypted = encrypter.encrypt(paramString, iv: finalIv);
          resStr = base64Encode(encrypted.bytes);
        } else {
          var decryptStr = encryption.Encrypted.fromBase64(paramString);
          resStr = encrypter.decrypt(decryptStr, iv: finalIv);
        }
        debugPrint("${type == 0 ? '<加密>' : '<解密>'}后的字符串:${resStr}");
        return resStr;
      } catch (e) {
        // 加解密失败
        return content;
      }
    }
    return content;
  }
}
