package com.niimbot.niimbot_log_plugin.model

import java.util.*

class NiimbotLogModel {
    var uid: String? = null
    var eventType: String? = null
    var eventLevel: Int = 0
    var eventName: String? = null
    var eventExplain: String? = null
    var createTime: Date? = null
    var city: String? = null
    var phone: String? = null
    var deviceId: String? = null
    var eventDetail: MutableMap<String, Any>? = null
    var systemVersion: String? = null
    var appVersion: String? = null
    var language: String? = null
    var moudle: String? = null
}