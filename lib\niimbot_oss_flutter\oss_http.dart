part of aliyun_oss_flutter;

/// HTTP 客户端工具类
var _http = _DioUtils.getInstance();

class _DioUtils {
  /// 获取 `Dio` 单例实例
  ///
  /// - 如果实例为空，则创建新的 `Dio` 对象，并设置默认超时时间
  /// - 添加日志拦截器 `LogInterceptor` 以便调试 HTTP 请求和响应
  static Dio getInstance() {
    if (_instance == null) {
      _instance = Dio(BaseOptions(
        connectTimeout: const Duration(seconds: 30), // 连接超时时间 30 秒
        receiveTimeout: const Duration(seconds: 30), // 接收超时时间 30 秒
      ));

      _instance!.interceptors.add(LogInterceptor(responseBody: true)); // 启用日志拦截器
    }

    return _instance!;
  }

  static Dio? _instance; // `Dio` 单例实例
}

// 忽略 `use_late_for_private_fields_and_variables` 规则
