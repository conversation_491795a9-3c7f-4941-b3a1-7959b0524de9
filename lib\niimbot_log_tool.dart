import './niimbot_log_manager.dart';
import 'channel/niimbot_log_plugin_platform_interface.dart';
import 'model/niimbot_app_config.dart';
import 'model/niimbot_log_result.dart';

enum NiimbotLogSenceType {
  allLog, //全局日志
  sdkLog, //SDK日志
  rfidPrintLog, //rfid打印日志
}

enum NiimbotLogSTSType {
  printLogSTS, //SDK打印日志 STS
  appLogSTS, //app全局日志STS
  printContentSTS, //打印内容STS
}

extension NiimbotLogSTSTypeExtension on NiimbotLogSTSType {
  String getStringValue() {
    switch (this) {
      case NiimbotLogSTSType.printLogSTS:
        return 'sts/slsToken/print/app/log';
      case NiimbotLogSTSType.appLogSTS:
        return 'sts/slsToken/print/app/log';
      case NiimbotLogSTSType.printContentSTS:
        return 'sts/slsToken/print/bison/content';
      default:
        return 'sts/slsToken/print/app/log';
    }
  }
}

class NiimbotLogTool {
  ///初始化日志服务 AppLogPluginConfig(apiBaseUrl: "", userAgent: "", language: "")
  static initLogConfig(NiimbotAppConfig appConfig) {
    NiimbotLogManager.instance.initConfig(appConfig);
  }

  ///更新deviceId
  static updateDeviceId(String deviceId, String agent) {
    NiimbotLogManager.instance.updateDeviceId(deviceId, agent);
  }

  ///更新用户信息
  static updateConfigInfo(String token, String uid, String languageCode, String currentConnectPrint) {
    token = ((token.contains("bearer") || token.isEmpty) ? token : "bearer $token");
    NiimbotLogManager.instance.updateConfig(token, uid, languageCode, currentConnectPrint);
  }

  ///即时上传日志
  ///[logInfo] 需上传的日志信息
  ///[logSence] 日志场景 用于非即时上传 区分写入文件
  static Future<bool> uploadLogInstantTime(Map<String, dynamic> logInfo,
      {NiimbotLogSTSType logSTSType = NiimbotLogSTSType.appLogSTS, String topic = ""}) {
    //直接上传
    String stsPath = logSTSType.getStringValue();
    return NiimbotLogManager.instance.addAppLogInstandTime(logInfo, path: stsPath, topic: topic);
  }

  ///写入日志至本地日志文件
  ///[logInfo] 需写入的日志信息
  ///[logSence] 日志场景 用于非即时上传 区分写入文件
  static Future<bool> writeLogToFile(Map<String, dynamic> logInfo,
      {NiimbotLogSenceType logSence = NiimbotLogSenceType.allLog}) {
    //写入本地文件
    return NiimbotLogManager.instance.writeAppLogTofile(logInfo, path: NiimbotLogSTSType.appLogSTS.getStringValue());
  }

  ///手动上传日志
  ///[success] 成功回调上传结果 返回日志文件在oss地址
  ///[faild] 失败回调 返回失败错误码及失败信息
  ///[filePath] 上传日志文件地址 可选，不传默认上传app全局日志文件
  ///[logSence] 日志场景
  static uploadLogManual(
      {Function(String)? success,
      Function(NiimbotLogResultType, String)? faild,
      String? filePath,
      String topic = "appLog",
      NiimbotLogSenceType logSence = NiimbotLogSenceType.allLog}) {
    NiimbotLogManager.instance.uploadFile((NiimbotLogResultType resultCode, String value) async {
      if (resultCode != NiimbotLogResultType.success) {
        faild?.call(resultCode, value);
      } else {
        //日志文件上传成后 将文件url以日志消息方式发送至SLS
        bool isSuccess = await uploadLogInstantTime({"logUrl": value}, topic: topic);
        if (!isSuccess) {
          faild?.call(resultCode, "");
        } else {
          success?.call(value);
        }
      }
    }, filePath: filePath ?? '', needDeleteFile: true);
  }

  ///上传文件
  ///[success] 成功回调上传结果 返回日志文件在oss地址
  ///[faild] 失败回调 返回失败错误码及失败信息
  ///[filePath] 上传文件地址
  static uploadFile(
    Function(String) success,
    String filePath, {
    Function(NiimbotLogResultType, String)? faild,
    String? fileName,
  }) {
    NiimbotLogManager.instance.uploadFile((NiimbotLogResultType resultCode, String value) {
      if (resultCode != NiimbotLogResultType.success) {
        faild?.call(resultCode, "");
      } else {
        success.call(value);
      }
    }, filePath: filePath, fileName: fileName);
  }
}
