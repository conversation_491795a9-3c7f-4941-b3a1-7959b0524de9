import 'package:flutter/services.dart';

import 'niimbot_log_plugin_platform_interface.dart';

/// An implementation of [NiimbotLogPluginPlatform] that uses method channels.
class MethodChannelNiimbotLogPlugin extends NiimbotLogPluginPlatform {
  static const String METHOD_CHANNEL_NAME = 'niimbot_log_plugin';
  static const String METHOD_GET_PLATFORM_VERSION = 'getPlatformVersion';
  static const String METHOD_RECEIVE_NATIVE_DATA = 'receiveNativeData';

  final MethodChannel methodChannel = MethodChannel(METHOD_CHANNEL_NAME);

  MethodChannelNiimbotLogPlugin() {
    setupNativeCommunication();
  }

  @override
  Future<String?> getPlatformVersion() async {
    try {
      final version = await methodChannel.invokeMethod<String>(METHOD_GET_PLATFORM_VERSION);
      return version;
    } on Exception catch (e) {
      // 根据实际情况处理异常，例如记录日志或返回默认值
      print('Failed to get platform version: $e');
      return null;
    }
  }

  void setupNativeCommunication() {
    methodChannel.setMethodCallHandler((call) async {
      if (call.method.isEmpty) {
        print('Empty method call received');
        return;
      }

      switch (call.method) {
        case METHOD_RECEIVE_NATIVE_DATA:
          print("datassssssssssssssssssss");
          break;
        default:
          print("Unknown method: ${call.method}");
      }
    });
  }
}
