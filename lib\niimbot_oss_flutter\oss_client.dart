part of aliyun_oss_flutter;

/// 阿里云 OSS 客户端
/// 用于管理对象存储服务，包括初始化、上传文件等功能
class OSSClient {
  /// 工厂构造函数，返回单例实例
  factory OSSClient() {
    return _instance!;
  }

  /// 私有构造函数，用于初始化 OSS 客户端
  OSSClient._({
    required this.endpoint,
    required this.bucket,
    required this.credentials,
  }) {
    _signer = null; // 初始化时清空签名器
  }

  /// 初始化 OSS 客户端
  ///
  /// * [endpoint] OSS 访问域名
  /// * [bucket] OSS 存储桶名称
  /// * [credentials] 获取凭证的异步回调函数
  /// * [dio] 可选的 HTTP 客户端实例
  ///
  /// **注意**: 重新初始化会清空 `_signer`，上传前会重新拉取 OSS 信息。
  static OSSClient init({
    required String endpoint,
    required String bucket,
    required Future<Credentials> Function() credentials,
    Dio? dio,
  }) {
    _instance = OSSClient._(
      endpoint: endpoint,
      bucket: bucket,
      credentials: credentials,
    );
    if (dio != null) {
      _http = dio;
    }
    return _instance!;
  }

  static OSSClient? _instance; // OSSClient 单例实例

  Signer? _signer; // 签名器实例，用于请求签名

  final String endpoint; // OSS 访问域名
  final String bucket; // OSS 存储桶名称
  final Future<Credentials> Function() credentials; // 获取 OSS 凭证的回调

  /// 上传对象到 OSS
  ///
  /// * [object] 要上传的 OSS 对象
  /// * [bucket] 可选的存储桶，默认为初始化时的 `bucket`
  /// * [endpoint] 可选的 OSS 访问域名，默认为初始化时的 `endpoint`
  /// * [path] 可选的上传路径，若为空则自动生成
  ///
  /// **返回**: 上传成功的 `OSSObject`
  Future<OSSObject> putObject({
    required OSSObject object,
    String? bucket,
    String? endpoint,
    String? path,
  }) async {
    // 先进行凭证验证
    _signer = await verify();

    // 获取资源路径
    final String objectPath = object.resourcePath(path);
    DateTime dateTime = _signer!.credentials.expiration ?? DateTime.now();
    final String string = DateFormat('EEE, dd MMM yyyy HH:mm:ss', 'en_ISO').format(dateTime.toUtc());

    // 生成安全请求头
    final Map<String, dynamic> safeHeaders = _signer!
        .sign(
            httpMethod: 'PUT',
            resourcePath: '/${bucket ?? this.bucket}/$objectPath',
            headers: {
              'content-type': object.mediaType.mimeType,
            },
            dateString: "$string GMT")
        .toHeaders();

    try {
      // 生成上传 URL
      final String url = 'https://${bucket ?? this.bucket}.${endpoint ?? this.endpoint}/$objectPath';
      final Uint8List bytes = object.bytes;

      // 发送 PUT 请求上传数据
      await _http.put<void>(
        url,
        data: Stream.fromIterable(bytes.map((e) => [e])),
        options: Options(
          headers: <String, dynamic>{
            ...safeHeaders,
            ...<String, dynamic>{
              'content-length': object.length,
            }
          },
          contentType: object._mediaType.mimeType,
        ),
      );

      // 上传成功后返回对象
      return object..uploadSuccessful(url);
    } catch (e) {
      rethrow; // 捕获异常并重新抛出
    }
  }

  /// 验证 OSS 访问凭证
  ///
  /// * 如果 `_signer` 为空，则重新获取凭证
  /// * 如果使用 `securityToken` 进行鉴权，则检查是否过期
  /// * 如果 `securityToken` 丢失，则清空
  ///
  /// **返回**: `Signer` 认证信息
  Future<Signer> verify() async {
    // 首次使用，获取新的签名器
    if (_signer == null) {
      _signer = Signer(await credentials.call());
    } else {
      // 检查 securityToken 是否过期
      if (_signer!.credentials.useSecurityToken) {
        if (_signer!.credentials.expiration!.isBefore(DateTime.now().toUtc())) {
          _signer = Signer(await credentials.call());
        }
      } else {
        // 如果 expiration 或 securityToken 丢失，则清空
        _signer!.credentials.clearSecurityToken();
      }
    }
    return _signer!;
  }
}
