# Flutter日志上报组件库

> Flutter日志上报组件库

## 使用教程
> 组件依赖[sls组件](https://git.jc-ai.cn/print/foundation/niimbot_sls_flutter.git)上传打印日志到SLS，

### 1.组件初始化
在Application.dart文件中，接入NiimbotAPM.initLogConfig(....)

    NiimbotLogTool.initLogConfig(NiimbotAppConfig(
            apiBaseUrl: "https://print.jc-test.cn/api/",
            phone: "iphone 15",
            systemVersion: "17.4.1",
            appVserson: "6.5.0",
            os: "ios",
            deviceId: "deoekogekogkeokgeo",
            uid: "6R87Y9TE",
            userAgent:
                "AppId/com.suofang.jcbqdy OS/ios AppVersionName/6.0.8 Model/iPhone-11 SystemVersion/14.6 DeviceId/EA400739-C714-45B2-9A73-100B8AAEBFC1 boundleId/com.suofang.jcbqdy referer/CP001Mobile",
            language: "en"));
    

### 2.即时上传打印日志
    在任何场景直接调用插件方法 即时上传日志
    ///即时上传日志信息
    ///[logInfo]日志信息
    ///[topic]日志话题/功能模块
    Future<bool> uploadLogInstantTime(Map<String, dynamic> logInfo, {String topic = ""}) async {
        bool uploadSuccess = await NiimbotLogTool.uploadLogInstantTime(logInfo, topic: topic);
        return Future.value(uploadSuccess);
    }

### 3.写入日志到本地缓存文件
    ///写入日志信息至本地日志文件
    ///[logInfo]日志信息
    Future<bool> writeLogToFile(Map<String, dynamic> logInfo) async {
        bool uploadSuccess = await NiimbotLogTool.writeLogToFile(logInfo);
        return Future.value(uploadSuccess);
    }

### 4.手动上传日志
    ///写入日志信息至本地日志文件
    ///[topic]日志话题/全局日志appLog、SDK日志SDKLog
    ///[localPath]日志文件地址，默认全局日志用不传，SDK日志需传入日志文件地址
    Future<Map> uploadLogFileToSls({String topic = "appLog", String? localPath}) async {
        Completer<Map> completer = Completer();
        NiimbotLogTool.uploadLogManual(
            success: (p0) {
                completer.complete({"writeLogInfoSuccess": true, "url": p0});
            },
            faild: (p0, p1) {
                completer.complete({"writeLogInfoSuccess": false, "errorMsg": p1});
            },
            topic: topic,
            filePath: localPath);
        return completer.future;
    }
done.

