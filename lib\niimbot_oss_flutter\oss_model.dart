part of aliyun_oss_flutter;

/// 阿里云 OSS 凭证管理
class Credentials {
  Credentials({
    required this.accessKeyId,
    required this.accessKeySecret,
    this.securityToken,
    this.expiration,
  }) {
    if (!useSecurityToken) {
      clearSecurityToken();
    }
  }

  /// 通过 JSON 字符串创建 `Credentials`
  factory Credentials.fromJson(String str) => Credentials.fromMap(json.decode(str) as Map<String, dynamic>);

  /// 通过 Map 创建 `Credentials`
  factory Credentials.fromMap(Map<String, dynamic> json) {
    return Credentials(
      accessKeyId: json['access_key_id'] as String,
      accessKeySecret: json['access_key_secret'] as String,
      securityToken: json['security_token'] as String?,
      expiration: json['expiration'] != null ? DateTime.parse(json['expiration'] as String) : null,
    );
  }

  final String accessKeyId;
  final String accessKeySecret;
  String? securityToken;
  DateTime? expiration;

  /// 判断是否使用 SecurityToken
  bool get useSecurityToken => securityToken != null && expiration != null;

  /// 清除 SecurityToken
  void clearSecurityToken() {
    securityToken = null;
    expiration = null;
  }
}

/// OSS 对象抽象类
abstract class OSSObject {
  OSSObject._({
    required this.bytes,
    MediaType? mediaType,
    this.uuid,
  }) : _mediaType = mediaType ?? MediaType('application', 'octet-stream');

  final Uint8List bytes;
  final MediaType _mediaType;
  MediaType get mediaType => _mediaType;
  final String? uuid;
  String url = '';

  /// 获取文件长度
  int get length => bytes.lengthInBytes;

  /// 获取文件类型
  String get type => _mediaType == MediaType('application', 'octet-stream') ? 'file' : _mediaType.type;

  /// 生成文件名
  String get name => (uuid ?? const Uuid().v1()) + (type == 'file' ? '' : '.${_mediaType.subtype}');

  /// 生成文件夹路径
  String get folderPath => [
        type,
        DateFormat('y/MM/dd').format(DateTime.now()),
      ].join('/');

  /// 生成资源路径
  String resourcePath(String? path) => '${path ?? folderPath}/$name';

  /// 上传成功后设置 URL
  void uploadSuccessful(String url) {
    this.url = url;
  }
}

/// OSS 图片对象
class OSSImageObject extends OSSObject {
  OSSImageObject._({
    required Uint8List bytes,
    required MediaType mediaType,
    String? uuid,
  }) : super._(bytes: bytes, mediaType: mediaType, uuid: uuid);

  factory OSSImageObject.fromBytes({
    required Uint8List bytes,
    required MediaType mediaType,
    String? uuid,
  }) {
    return OSSImageObject._(
      bytes: bytes,
      mediaType: mediaType,
      uuid: uuid,
    );
  }

  factory OSSImageObject.fromFile({
    required File file,
    String? uuid,
  }) {
    String subtype = path.extension(file.path).toLowerCase().replaceFirst('.', '') ?? '*';
    return OSSImageObject._(
      bytes: file.readAsBytesSync(),
      mediaType: MediaType('image', subtype),
      uuid: uuid,
    );
  }
}

/// OSS 音频对象
class OSSAudioObject extends OSSObject {
  OSSAudioObject._({
    required Uint8List bytes,
    required MediaType mediaType,
    String? uuid,
  }) : super._(bytes: bytes, mediaType: mediaType, uuid: uuid);

  factory OSSAudioObject.fromBytes({
    required Uint8List bytes,
    required MediaType mediaType,
    String? uuid,
  }) {
    return OSSAudioObject._(
      bytes: bytes,
      mediaType: mediaType,
      uuid: uuid,
    );
  }

  factory OSSAudioObject.fromFile({
    required File file,
    String? uuid,
  }) {
    String subtype = path.extension(file.path).toLowerCase().replaceFirst('.', '') ?? '*';
    return OSSAudioObject._(
      bytes: file.readAsBytesSync(),
      mediaType: MediaType('audio', subtype),
      uuid: uuid,
    );
  }
}

/// OSS 视频对象
class OSSVideoObject extends OSSObject {
  OSSVideoObject._({
    required Uint8List bytes,
    required MediaType mediaType,
    String? uuid,
  }) : super._(bytes: bytes, mediaType: mediaType, uuid: uuid);

  factory OSSVideoObject.fromBytes({
    required Uint8List bytes,
    required MediaType mediaType,
    String? uuid,
  }) {
    return OSSVideoObject._(
      bytes: bytes,
      mediaType: mediaType,
      uuid: uuid,
    );
  }

  factory OSSVideoObject.fromFile({
    required File file,
    String? uuid,
  }) {
    String subtype = path.extension(file.path).toLowerCase().replaceFirst('.', '') ?? '*';
    return OSSVideoObject._(
      bytes: file.readAsBytesSync(),
      mediaType: MediaType('video', subtype),
      uuid: uuid,
    );
  }
}
