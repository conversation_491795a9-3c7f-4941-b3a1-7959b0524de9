name: niimbot_log_plugin
description: A new Flutter project.
version: 0.0.1
homepage:
publish_to: 'none' # Remove this line if you wish to publish to pub.dev
environment:
  sdk: '>=3.1.0 <4.0.0'
  flutter: ">=3.19.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  plugin_platform_interface: ^2.0.2
#  aliyun_log_dart_sdk: ^1.0.0
  niimbot_sls_flutter:
    git:
      url: https://git.jc-ai.cn/print/foundation/niimbot_sls_flutter.git
      ref: 'master'

  dio: ^5.0.0
  path_provider: ^2.0.6
  flutter_easyloading: ^3.0.5
  encrypt: ^5.0.1
  crypto: ^3.0.3
  intl: ^0.19.0
  uuid: ^4.0.0
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # This section identifies this Flutter project as a plugin project.
  # The 'pluginClass' specifies the class (in Java, Kotlin, Swift, Objective-C, etc.)
  # which should be registered in the plugin registry. This is required for
  # using method channels.
  # The Android 'package' specifies package in which the registered class is.
  # This is required for using method channels on Android.
  # The 'ffiPlugin' specifies that native code should be built and bundled.
  # This is required for using `dart:ffi`.
  # All these are used by the tooling to maintain consistency when
  # adding or updating assets for this project.
  plugin:
    platforms:
      android:
        package: com.niimbot.niimbot_log_plugin
        pluginClass: NiimbotLogPlugin
      ios:
        pluginClass: NiimbotLogPlugin
      linux:
        pluginClass: NiimbotLogPlugin
      macos:
        pluginClass: NiimbotLogPlugin
      windows:
        pluginClass: NiimbotLogPluginCApi
      web:
        pluginClass: NiimbotLogPluginWeb
        fileName: niimbot_log_plugin_web.dart

  # To add assets to your plugin package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # To add custom fonts to your plugin package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
