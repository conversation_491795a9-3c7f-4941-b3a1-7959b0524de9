#ifndef FLUTTER_PLUGIN_NIIMBOT_LOG_PLUGIN_H_
#define FLUTTER_PLUGIN_NIIMBOT_LOG_PLUGIN_H_

#include <flutter/method_channel.h>
#include <flutter/plugin_registrar_windows.h>

#include <memory>

namespace niimbot_log_plugin {

class NiimbotLogPlugin : public flutter::Plugin {
 public:
  static void RegisterWithRegistrar(flutter::PluginRegistrarWindows *registrar);

  NiimbotLogPlugin();

  virtual ~NiimbotLogPlugin();

  // Disallow copy and assign.
  NiimbotLogPlugin(const NiimbotLogPlugin&) = delete;
  NiimbotLogPlugin& operator=(const NiimbotLogPlugin&) = delete;

 private:
  // Called when a method is called on this plugin's channel from Dart.
  void HandleMethodCall(
      const flutter::MethodCall<flutter::EncodableValue> &method_call,
      std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result);
};

}  // namespace niimbot_log_plugin

#endif  // FLUTTER_PLUGIN_NIIMBOT_LOG_PLUGIN_H_
