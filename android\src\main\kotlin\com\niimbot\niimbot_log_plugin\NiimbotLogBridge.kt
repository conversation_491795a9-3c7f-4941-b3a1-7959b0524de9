package com.niimbot.niimbot_log_plugin

import com.google.gson.Gson
import com.niimbot.niimbot_log_plugin.model.NiimbotLogModel
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.MethodChannel
import java.util.HashMap

class NiimbotLogBridge
   {
    companion object {
        private lateinit var channel: MethodChannel

        val METHOD_RECEIVE_NATIVE_DATA:String = "receiveNativeData";
        fun getInstance(): NiimbotLogBridge {
            return NiimbotLogBridge()
        }
    }

      fun initChannel(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding): NiimbotLogBridge {
         channel = MethodChannel(flutterPluginBinding.binaryMessenger, "niimbot_log_plugin")

          return this
       }

       fun setMethodCallHandler(handler: MethodChannel.MethodCallHandler) {
           channel.setMethodCallHandler(handler)
       }

       fun onDetachedFromEngine(){
           channel.setMethodCallHandler(null)
       }

       fun sendNativeLogData(name:String, moudle:String, level:Int=1,  explain:String="", detail:MutableMap<String,Any> =mutableMapOf()){
           val model = NiimbotLogModel()
           model.eventType="nativeFile"
           model.eventName=name
           model.moudle=moudle
           model.eventLevel=level
           if(explain.isNotEmpty()){
               model.eventExplain=explain
           }
           if(detail.isNotEmpty()){
               model.eventDetail= detail as HashMap<String, Any>?
           }

           invokeLogData(model)
       }

       fun sendLogData(name:String, moudle:String, level:Int=1,  explain:String="", detail:MutableMap<String,Any> =mutableMapOf()){
           val model = NiimbotLogModel()
           model.eventType="business"
           model.eventName=name
           model.moudle=moudle
           model.eventLevel=level
           if(explain.isNotEmpty()){
               model.eventExplain=explain
           }
           if(detail.isNotEmpty()){
               model.eventDetail= detail as HashMap<String, Any>?
           }

           invokeLogData(model)
       }

       fun invokeLogData(model: NiimbotLogModel) {
           val gson = Gson()
           val json = gson.toJson(model)
           channel.invokeMethod(METHOD_RECEIVE_NATIVE_DATA,json)
       }


}