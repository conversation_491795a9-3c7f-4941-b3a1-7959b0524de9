//
//  Generated file. Do not edit.
//

// clang-format off

#include "generated_plugin_registrant.h"

#include <niimbot_log_plugin/niimbot_log_plugin.h>

void fl_register_plugins(FlPluginRegistry* registry) {
  g_autoptr(FlPluginRegistrar) niimbot_log_plugin_registrar =
      fl_plugin_registry_get_registrar_for_plugin(registry, "NiimbotLogPlugin");
  niimbot_log_plugin_register_with_registrar(niimbot_log_plugin_registrar);
}
