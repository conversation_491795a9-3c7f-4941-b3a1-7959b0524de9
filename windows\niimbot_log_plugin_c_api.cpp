#include "include/niimbot_log_plugin/niimbot_log_plugin_c_api.h"

#include <flutter/plugin_registrar_windows.h>

#include "niimbot_log_plugin.h"

void NiimbotLogPluginCApiRegisterWithRegistrar(
    FlutterDesktopPluginRegistrarRef registrar) {
  niimbot_log_plugin::NiimbotLogPlugin::RegisterWithRegistrar(
      flutter::PluginRegistrarManager::GetInstance()
          ->GetRegistrar<flutter::PluginRegistrarWindows>(registrar));
}
